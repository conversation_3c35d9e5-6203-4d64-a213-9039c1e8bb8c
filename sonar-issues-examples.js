// ========== SonarQube 检测问题示例 ==========

// 1. 【认知复杂度过高】- 嵌套过深
function processUserData(user, options) {
  if (user) {
    if (user.isActive) {
      if (user.permissions) {
        if (user.permissions.length > 0) {
          for (let i = 0; i < user.permissions.length; i++) {
            if (user.permissions[i].type === 'admin') {
              if (options && options.strictMode) {
                if (user.lastLogin) {
                  if (new Date() - user.lastLogin < 86400000) {
                    return { status: 'valid', level: 'admin' };
                  } else {
                    return { status: 'expired', level: 'admin' };
                  }
                }
              }
            }
          }
        }
      }
    }
  }
  return { status: 'invalid' };
}

// 2. 【代码重复】- 重复的代码块
function validateEmail(email) {
  if (!email) {
    console.log('Email validation failed: empty email');
    return false;
  }
  if (email.length < 5) {
    console.log('Email validation failed: too short');
    return false;
  }
  if (!email.includes('@')) {
    console.log('Email validation failed: missing @');
    return false;
  }
  return true;
}

function validatePhone(phone) {
  if (!phone) {
    console.log('Phone validation failed: empty phone');
    return false;
  }
  if (phone.length < 10) {
    console.log('Phone validation failed: too short');
    return false;
  }
  if (!/^\d+$/.test(phone)) {
    console.log('Phone validation failed: invalid format');
    return false;
  }
  return true;
}

// 3. 【函数参数过多】
function createUser(firstName, lastName, email, phone, address, city, state, 
                   zipCode, country, birthDate, gender, occupation, company) {
  return {
    firstName, lastName, email, phone, address, city, state,
    zipCode, country, birthDate, gender, occupation, company
  };
}

// 4. 【魔法数字】- 硬编码的数字
function calculateDiscount(price, userType) {
  if (userType === 'premium') {
    return price * 0.15; // 魔法数字
  } else if (userType === 'gold') {
    return price * 0.10; // 魔法数字
  } else if (userType === 'silver') {
    return price * 0.05; // 魔法数字
  }
  return 0;
}

// 5. 【空的catch块】
function riskyOperation() {
  try {
    JSON.parse('invalid json');
  } catch (e) {
    // 空的catch块 - SonarQube会报警
  }
}

// 6. 【未使用的变量】
function processData(data) {
  const unusedVariable = 'this is not used'; // 未使用的变量
  const result = data.map(item => item.value);
  return result;
}

// 7. 【相同的条件分支】
function getStatus(code) {
  if (code === 200) {
    return 'success';
  } else if (code === 201) {
    return 'success'; // 相同的返回值
  } else if (code === 202) {
    return 'success'; // 相同的返回值
  } else {
    return 'error';
  }
}

// 8. 【过长的函数】- 超过建议行数
function massiveFunction(data) {
  // 模拟一个很长的函数
  let result = [];
  
  // 第一部分处理
  for (let i = 0; i < data.length; i++) {
    if (data[i].type === 'A') {
      result.push(data[i].value * 2);
    }
  }
  
  // 第二部分处理
  for (let i = 0; i < data.length; i++) {
    if (data[i].type === 'B') {
      result.push(data[i].value * 3);
    }
  }
  
  // 第三部分处理
  for (let i = 0; i < data.length; i++) {
    if (data[i].type === 'C') {
      result.push(data[i].value * 4);
    }
  }
  
  // 第四部分处理
  for (let i = 0; i < data.length; i++) {
    if (data[i].type === 'D') {
      result.push(data[i].value * 5);
    }
  }
  
  // 数据验证
  result = result.filter(item => item > 0);
  result = result.sort((a, b) => a - b);
  
  // 格式化输出
  const formatted = result.map(item => ({
    value: item,
    formatted: `$${item.toFixed(2)}`
  }));
  
  return formatted;
}

// 9. 【Switch语句缺少default】
function handleAction(action) {
  switch (action) {
    case 'create':
      return 'Creating...';
    case 'update':
      return 'Updating...';
    case 'delete':
      return 'Deleting...';
    // 缺少default分支
  }
}

// 10. 【使用==而不是===】
function compareValues(a, b) {
  if (a == b) { // 应该使用 ===
    return true;
  }
  return false;
}

// 11. 【变量命名不规范】
function process_data(input_data) { // 应该使用驼峰命名
  const temp_var = input_data.filter(x => x.active); // 变量名不清晰
  return temp_var;
}

// 12. 【过度嵌套的三元运算符】
function getStatusMessage(user) {
  return user ? 
    (user.isActive ? 
      (user.isPremium ? 
        (user.hasAccess ? 'Premium Active User' : 'Premium No Access') 
        : 'Regular Active User') 
      : 'Inactive User') 
    : 'No User';
}

// 13. 【重复的字符串字面量】
function logMessages() {
  console.log('User authentication failed');
  console.log('User authentication failed'); // 重复的字符串
  console.log('User authentication failed'); // 重复的字符串
}

// 14. 【复杂的布尔表达式】
function checkAccess(user, resource, time) {
  if (user && user.isActive && user.permissions && 
      user.permissions.includes('read') && resource && 
      resource.isPublic || (resource.owner === user.id && 
      time > user.lastLogin && user.subscription === 'premium')) {
    return true;
  }
  return false;
}

// 15. 【函数返回类型不一致】
function getValue(condition) {
  if (condition) {
    return 'success'; // 返回字符串
  } else if (condition === null) {
    return 0; // 返回数字
  } else {
    return false; // 返回布尔值
  }
}
