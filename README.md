# Vue 3.6 Vapor 模式示例项目

这是一个基于 Vue 3.6 beta 版本的 Vapor 模式示例项目，展示了 Vue Vapor 的各种特性和性能优势。

## 🚀 Vue Vapor 简介

Vue Vapor 是 Vue 3.6 中引入的新编译模式，它提供了：

- **更小的包体积**: 通过编译时优化减少运行时代码
- **更快的渲染性能**: 直接操作 DOM，减少虚拟 DOM 开销
- **更好的内存效率**: 减少对象创建和垃圾回收压力
- **向后兼容**: 可以与传统 Vue 组件混合使用

## 📁 项目结构

```
src/
├── App.vapor.vue              # 主应用组件 (Vapor 模式)
├── components/
│   ├── VaporCounter.vapor.vue # 计数器组件演示
│   └── PerformanceDemo.vapor.vue # 性能测试组件
├── main.ts                    # 应用入口
└── vite-env.d.ts             # TypeScript 声明
```

## 🎯 功能特性

### 1. 基础功能演示
- 响应式状态管理 (`ref`, `computed`)
- 事件处理和方法定义
- 条件渲染和列表渲染
- 双向数据绑定

### 2. 组件化演示
- Vapor 模式组件定义
- Props 和 Emits 的使用
- 组件间通信
- 可复用组件设计

### 3. 性能测试
- 大量数据渲染测试
- 实时性能监控
- 批量更新演示
- 渲染时间统计

## 🛠️ 技术栈

- **Vue 3.6.0-alpha.1**: 支持 Vapor 模式的 Vue 版本
- **TypeScript**: 类型安全的 JavaScript
- **Vite**: 快速的构建工具
- **Vue Vapor**: 新的编译模式

## 📦 安装和运行

```bash
# 安装依赖
pnpm install

# 启动开发服务器
pnpm dev

# 构建生产版本
pnpm build

# 预览生产版本
pnpm preview
```

## 🔧 Vapor 模式使用方法

### 1. 组件定义
```vue
<script lang="ts" vapor>
import { ref, computed } from 'vue'

const count = ref(0)
const doubleCount = computed(() => count.value * 2)

const increment = () => {
  count.value++
}
</script>

<template>
  <div>
    <span>{{ count }}</span>
    <button @click="increment">+</button>
  </div>
</template>
```

### 2. 应用创建
```typescript
import { createVaporApp } from 'vue'
import App from './App.vapor.vue'

createVaporApp(App).mount('#app')
```

## 🎨 示例功能

### 基础功能
- ✅ 响应式计数器
- ✅ 待办事项管理
- ✅ 计算属性演示
- ✅ 事件处理

### 组件演示
- ✅ 可配置计数器组件
- ✅ Props 和 Emits
- ✅ 组件状态管理
- ✅ 样式和动画

### 性能测试
- ✅ 大量数据渲染 (1000-10000 项)
- ✅ 实时性能监控
- ✅ 批量更新测试
- ✅ 渲染时间统计

## 📊 性能优势

相比传统 Vue 组件，Vapor 模式提供：

- **包体积减少**: 约 30-50% 的包体积优化
- **渲染性能提升**: 2-3x 的渲染性能提升
- **内存使用优化**: 减少 40-60% 的内存占用
- **启动时间**: 更快的应用启动时间

## 🔍 开发工具支持

- **VS Code**: 推荐使用 Vue - Official 扩展
- **TypeScript**: 完整的类型检查支持
- **Vue DevTools**: 支持 Vapor 组件调试
- **HMR**: 热模块替换支持

## 📚 学习资源

- [Vue 3.6 官方文档](https://vuejs.org/)
- [Vapor 模式指南](https://github.com/vuejs/core-vapor)
- [TypeScript 支持](https://vuejs.org/guide/typescript/)
- [Vite 构建工具](https://vitejs.dev/)

## 🤝 贡献

欢迎提交 Issue 和 Pull Request 来改进这个示例项目！

## 📄 许可证

MIT License
