<script lang="ts" vapor>
import { ref, computed, defineProps, defineEmits } from 'vue'

// 定义 props
const props = defineProps<{
  initialValue?: number
  step?: number
  max?: number
  min?: number
}>()

// 定义 emits
const emit = defineEmits<{
  change: [value: number]
  reachMax: [value: number]
  reachMin: [value: number]
}>()

// 响应式状态
const count = ref(props.initialValue || 0)

// 计算属性
const isAtMax = computed(() => 
  props.max !== undefined && count.value >= props.max
)
const isAtMin = computed(() => 
  props.min !== undefined && count.value <= props.min
)
const percentage = computed(() => {
  if (props.max === undefined || props.min === undefined) return 0
  const range = props.max - props.min
  const current = count.value - props.min
  return Math.round((current / range) * 100)
})

// 方法
const increment = () => {
  const step = props.step || 1
  const newValue = count.value + step
  
  if (props.max !== undefined && newValue > props.max) {
    count.value = props.max
    emit('reachMax', count.value)
  } else {
    count.value = newValue
  }
  
  emit('change', count.value)
}

const decrement = () => {
  const step = props.step || 1
  const newValue = count.value - step
  
  if (props.min !== undefined && newValue < props.min) {
    count.value = props.min
    emit('reachMin', count.value)
  } else {
    count.value = newValue
  }
  
  emit('change', count.value)
}

const reset = () => {
  count.value = props.initialValue || 0
  emit('change', count.value)
}
</script>

<template>
  <div class="vapor-counter">
    <div class="counter-header">
      <h3>Vapor 计数器组件</h3>
      <div class="counter-info">
        <span>步长: {{ step || 1 }}</span>
        <span v-if="min !== undefined">最小: {{ min }}</span>
        <span v-if="max !== undefined">最大: {{ max }}</span>
      </div>
    </div>
    
    <div class="counter-display">
      <div class="count-value">{{ count }}</div>
      <div v-if="max !== undefined && min !== undefined" class="progress-bar">
        <div 
          class="progress-fill" 
          :style="{ width: percentage + '%' }"
        ></div>
        <span class="progress-text">{{ percentage }}%</span>
      </div>
    </div>
    
    <div class="counter-controls">
      <button 
        @click="decrement" 
        :disabled="isAtMin"
        class="btn btn-secondary"
        :class="{ disabled: isAtMin }"
      >
        -{{ step || 1 }}
      </button>
      
      <button 
        @click="reset" 
        class="btn btn-outline"
      >
        重置
      </button>
      
      <button 
        @click="increment" 
        :disabled="isAtMax"
        class="btn btn-primary"
        :class="{ disabled: isAtMax }"
      >
        +{{ step || 1 }}
      </button>
    </div>
    
    <div class="counter-status">
      <div v-if="isAtMax" class="status-message max">
        已达到最大值！
      </div>
      <div v-if="isAtMin" class="status-message min">
        已达到最小值！
      </div>
    </div>
  </div>
</template>

<style scoped>
.vapor-counter {
  border: 2px solid #e0e0e0;
  border-radius: 12px;
  padding: 20px;
  background: linear-gradient(145deg, #ffffff, #f0f0f0);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  max-width: 400px;
  margin: 0 auto;
}

.counter-header {
  text-align: center;
  margin-bottom: 20px;
}

.counter-header h3 {
  margin: 0 0 10px 0;
  color: #333;
  font-size: 1.5em;
}

.counter-info {
  display: flex;
  justify-content: center;
  gap: 15px;
  font-size: 12px;
  color: #666;
}

.counter-info span {
  background: #f8f9fa;
  padding: 4px 8px;
  border-radius: 12px;
  border: 1px solid #dee2e6;
}

.counter-display {
  text-align: center;
  margin: 30px 0;
}

.count-value {
  font-size: 3em;
  font-weight: bold;
  color: #667eea;
  margin-bottom: 15px;
  text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
}

.progress-bar {
  position: relative;
  height: 20px;
  background: #e9ecef;
  border-radius: 10px;
  overflow: hidden;
  margin: 15px 0;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #667eea, #764ba2);
  transition: width 0.3s ease;
  border-radius: 10px;
}

.progress-text {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 12px;
  font-weight: bold;
  color: #333;
}

.counter-controls {
  display: flex;
  justify-content: center;
  gap: 15px;
  margin: 20px 0;
}

.btn {
  padding: 12px 20px;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 16px;
  font-weight: 600;
  transition: all 0.3s ease;
  min-width: 80px;
}

.btn:hover:not(.disabled) {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0,0,0,0.2);
}

.btn-primary {
  background: #667eea;
  color: white;
}

.btn-secondary {
  background: #6c757d;
  color: white;
}

.btn-outline {
  background: transparent;
  color: #667eea;
  border: 2px solid #667eea;
}

.btn-outline:hover {
  background: #667eea;
  color: white;
}

.btn.disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none !important;
  box-shadow: none !important;
}

.counter-status {
  min-height: 30px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.status-message {
  padding: 8px 16px;
  border-radius: 20px;
  font-weight: bold;
  font-size: 14px;
  animation: pulse 2s infinite;
}

.status-message.max {
  background: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
}

.status-message.min {
  background: #fff3cd;
  color: #856404;
  border: 1px solid #ffeaa7;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.7; }
}
</style>
