<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import VaporCounter from './components/VaporCounter.vapor.vue'
import VaporFeatures from './components/VaporFeatures.vapor.vue'
import PerformanceDemoVapor from './components/PerformanceDemo.vapor.vue'
import PerformanceDemo from './components/PerformanceDemo.vue'


// 响应式状态
const count = ref(0)
const name = ref('Vue Vapor')
const activeTab = ref('basic')
const todos = ref([
  { id: 1, text: '学习 Vue Vapor', completed: false },
  { id: 2, text: '构建示例应用', completed: true },
  { id: 3, text: '优化性能', completed: false }
])

// 计算属性
const doubleCount = computed(() => count.value * 2)
const completedTodos = computed(() =>
  todos.value.filter(todo => todo.completed)
)
const pendingTodos = computed(() =>
  todos.value.filter(todo => !todo.completed)
)

// 方法
const increment = () => {
  count.value++
}

const decrement = () => {
  count.value--
}

const toggleTodo = (id: number) => {
  const todo = todos.value.find(t => t.id === id)
  if (todo) {
    todo.completed = !todo.completed
  }
}

const addTodo = () => {
  const newTodo = {
    id: Date.now(),
    text: `新任务 ${todos.value.length + 1}`,
    completed: false
  }
  todos.value.push(newTodo)
}

const removeTodo = (id: number) => {
  const index = todos.value.findIndex(t => t.id === id)
  if (index > -1) {
    todos.value.splice(index, 1)
  }
}

// 组件事件处理
const handleCounterChange = (value: number) => {
  console.log('计数器值变化:', value)
}

const handleCounterReachMax = (value: number) => {
  console.log('计数器达到最大值:', value)
}

const handleCounterReachMin = (value: number) => {
  console.log('计数器达到最小值:', value)
}

// 监听器
watch(count, (newVal, oldVal) => {
  console.log(`计数从 ${oldVal} 变为 ${newVal}`)
})
</script>

<template>
  <div class="app">
    <header class="header">
      <h1>{{ name }} 示例</h1>
      <p>这是一个基于 Vue 3.6 Vapor 模式的示例应用</p>

      <!-- 标签页导航 -->
      <nav class="tabs">
        <button
          @click="activeTab = 'basic'"
          class="tab-btn"
          :class="{ active: activeTab === 'basic' }"
        >
          基础功能
        </button>
        <button
          @click="activeTab = 'components'"
          class="tab-btn"
          :class="{ active: activeTab === 'components' }"
        >
          组件演示
        </button>
        <button
          @click="activeTab = 'features'"
          class="tab-btn"
          :class="{ active: activeTab === 'features' }"
        >
          特性演示
        </button>
        <button
          @click="activeTab = 'performance'"
          class="tab-btn"
          :class="{ active: activeTab === 'performance' }"
        >
          Vapor 模式性能测试
        </button>
        <button
          @click="activeTab = 'performance-traditional'"
          class="tab-btn"
          :class="{ active: activeTab === 'performance-traditional' }"
        >
          传统模式性能测试
        </button>
      </nav>
    </header>

    <main class="main">
      <!-- 基础功能标签页 -->
      <div v-if="activeTab === 'basic'" class="tab-content">
        <!-- 计数器部分 -->
        <section class="counter-section">
          <h2>基础计数器</h2>
          <div class="counter">
            <button @click="decrement" class="btn btn-secondary">-</button>
            <span class="count-display">{{ count }}</span>
            <button @click="increment" class="btn btn-primary">+</button>
          </div>
          <p>双倍计数: <strong>{{ doubleCount }}</strong></p>
        </section>

        <!-- 待办事项部分 -->
        <section class="todos-section">
          <h2>待办事项</h2>
          <div class="todos-stats">
            <span>总计: {{ todos.length }}</span>
            <span>已完成: {{ completedTodos.length }}</span>
            <span>待完成: {{ pendingTodos.length }}</span>
          </div>

          <button @click="addTodo" class="btn btn-success">添加新任务</button>

          <div class="todos-list">
            <div
              v-for="todo in todos"
              :key="todo.id"
              class="todo-item"
              :class="{ completed: todo.completed }"
            >
              <input
                type="checkbox"
                :checked="todo.completed"
                @change="toggleTodo(todo.id)"
              />
              <span class="todo-text">{{ todo.text }}</span>
              <button
                @click="removeTodo(todo.id)"
                class="btn btn-danger btn-sm"
              >
                删除
              </button>
            </div>
          </div>
        </section>
      </div>

      <!-- 组件演示标签页 -->
      <div v-if="activeTab === 'components'" class="tab-content">
        <section class="components-section">
          <h2>Vapor 组件演示</h2>
          <div class="components-grid">
            <VaporCounter
              :initial-value="10"
              :step="5"
              :min="0"
              :max="100"
              @change="handleCounterChange"
              @reach-max="handleCounterReachMax"
              @reach-min="handleCounterReachMin"
            />
            <VaporCounter
              :initial-value="50"
              :step="10"
              :min="0"
              :max="200"
              @change="handleCounterChange"
            />
          </div>
        </section>
      </div>

      <!-- 特性演示标签页 -->
      <div v-if="activeTab === 'features'" class="tab-content">
        <VaporFeatures />
      </div>

      <!-- 性能测试标签页 -->
      <div v-if="activeTab === 'performance'" class="tab-content">
        <PerformanceDemoVapor />
      </div>

      <div v-if="activeTab === 'performance-traditional'" class="tab-content">  
        <PerformanceDemo />
      </div>
    </main>
  </div>
</template>

<style scoped>
.app {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.header {
  text-align: center;
  margin-bottom: 40px;
  padding: 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 10px;
}

.header h1 {
  margin: 0 0 10px 0;
  font-size: 2.5em;
}

.header p {
  margin: 0 0 20px 0;
  opacity: 0.9;
}

.tabs {
  display: flex;
  justify-content: center;
  gap: 10px;
  margin-top: 20px;
}

.tab-btn {
  padding: 10px 20px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  background: transparent;
  color: white;
  border-radius: 25px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 600;
  transition: all 0.3s ease;
}

.tab-btn:hover {
  background: rgba(255, 255, 255, 0.1);
  transform: translateY(-2px);
}

.tab-btn.active {
  background: white;
  color: #667eea;
  border-color: white;
}

.main {
  min-height: 500px;
}

.tab-content {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

.counter-section, .todos-section, .components-section {
  padding: 20px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  background: #f9f9f9;
  margin-bottom: 30px;
}

.counter-section h2, .todos-section h2, .components-section h2 {
  margin-top: 0;
  color: #333;
}

.components-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 30px;
  margin-top: 20px;
}

.counter {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 20px;
  margin: 20px 0;
}

.count-display {
  font-size: 2em;
  font-weight: bold;
  color: #667eea;
  min-width: 60px;
  text-align: center;
}

.btn {
  padding: 10px 20px;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  font-size: 16px;
  transition: all 0.3s ease;
}

.btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0,0,0,0.2);
}

.btn-primary {
  background: #667eea;
  color: white;
}

.btn-secondary {
  background: #6c757d;
  color: white;
}

.btn-success {
  background: #28a745;
  color: white;
  margin-bottom: 20px;
}

.btn-danger {
  background: #dc3545;
  color: white;
}

.btn-sm {
  padding: 5px 10px;
  font-size: 12px;
}

.todos-stats {
  display: flex;
  gap: 20px;
  margin-bottom: 20px;
  font-weight: bold;
}

.todos-stats span {
  padding: 5px 10px;
  background: #e9ecef;
  border-radius: 15px;
  font-size: 14px;
}

.todos-list {
  display: grid;
  gap: 10px;
}

.todo-item {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 15px;
  background: white;
  border-radius: 5px;
  border: 1px solid #dee2e6;
  transition: all 0.3s ease;
}

.todo-item:hover {
  box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}

.todo-item.completed {
  opacity: 0.7;
  background: #f8f9fa;
}

.todo-item.completed .todo-text {
  text-decoration: line-through;
  color: #6c757d;
}

.todo-text {
  flex: 1;
  font-size: 16px;
}

input[type="checkbox"] {
  width: 18px;
  height: 18px;
  cursor: pointer;
}
</style>