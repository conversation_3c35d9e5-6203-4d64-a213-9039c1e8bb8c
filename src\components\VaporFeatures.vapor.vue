<script lang="ts" vapor>
import { ref, computed, watch, onMounted, onUnmounted } from 'vue'

// 响应式状态演示
const message = ref('Hello Vue Vapor!')
const count = ref(0)
const isVisible = ref(true)
const selectedOption = ref('option1')
const inputValue = ref('')

// 列表数据
const items = ref([
  { id: 1, name: '苹果', price: 5.99, category: '水果' },
  { id: 2, name: '香蕉', price: 3.99, category: '水果' },
  { id: 3, name: '胡萝卜', price: 2.99, category: '蔬菜' },
  { id: 4, name: '西兰花', price: 4.99, category: '蔬菜' }
])

// 计算属性演示
const doubleCount = computed(() => count.value * 2)
const expensiveItems = computed(() => 
  items.value.filter(item => item.price > 4)
)
const totalPrice = computed(() => 
  items.value.reduce((sum, item) => sum + item.price, 0)
)

// 方法演示
const increment = () => {
  count.value++
}

const addRandomItem = () => {
  const categories = ['水果', '蔬菜', '肉类', '饮料']
  const names = ['新商品', '特价商品', '热销商品', '推荐商品']
  
  const newItem = {
    id: Date.now(),
    name: `${names[Math.floor(Math.random() * names.length)]} ${items.value.length + 1}`,
    price: Math.round((Math.random() * 10 + 1) * 100) / 100,
    category: categories[Math.floor(Math.random() * categories.length)]
  }
  
  items.value.push(newItem)
}

const removeItem = (id: number) => {
  const index = items.value.findIndex(item => item.id === id)
  if (index > -1) {
    items.value.splice(index, 1)
  }
}

const toggleVisibility = () => {
  isVisible.value = !isVisible.value
}

// 监听器演示
watch(count, (newVal, oldVal) => {
  console.log(`计数从 ${oldVal} 变为 ${newVal}`)
})

watch(inputValue, (newVal) => {
  console.log('输入值变化:', newVal)
})

// 生命周期演示
onMounted(() => {
  console.log('VaporFeatures 组件已挂载')
})

onUnmounted(() => {
  console.log('VaporFeatures 组件已卸载')
})

// 定时器演示
let timer: number | null = null

const startTimer = () => {
  if (timer) return
  
  timer = setInterval(() => {
    count.value++
  }, 1000)
}

const stopTimer = () => {
  if (timer) {
    clearInterval(timer)
    timer = null
  }
}

const resetCount = () => {
  count.value = 0
  stopTimer()
}
</script>

<template>
  <div class="vapor-features">
    <div class="features-header">
      <h2>Vue Vapor 特性演示</h2>
      <p>展示 Vapor 模式支持的各种 Vue 特性</p>
    </div>

    <div class="features-grid">
      <!-- 响应式状态 -->
      <section class="feature-card">
        <h3>响应式状态</h3>
        <div class="demo-content">
          <div class="input-group">
            <label>消息:</label>
            <input v-model="message" type="text" />
          </div>
          <p class="output">{{ message }}</p>
          
          <div class="counter-demo">
            <span class="count">计数: {{ count }}</span>
            <div class="counter-controls">
              <button @click="increment" class="btn btn-primary">+1</button>
              <button @click="startTimer" class="btn btn-success">开始计时</button>
              <button @click="stopTimer" class="btn btn-warning">停止计时</button>
              <button @click="resetCount" class="btn btn-secondary">重置</button>
            </div>
          </div>
        </div>
      </section>

      <!-- 计算属性 -->
      <section class="feature-card">
        <h3>计算属性</h3>
        <div class="demo-content">
          <p>双倍计数: <strong>{{ doubleCount }}</strong></p>
          <p>商品总价: <strong>${{ totalPrice.toFixed(2) }}</strong></p>
          <p>高价商品数量: <strong>{{ expensiveItems.length }}</strong></p>
        </div>
      </section>

      <!-- 条件渲染 -->
      <section class="feature-card">
        <h3>条件渲染</h3>
        <div class="demo-content">
          <button @click="toggleVisibility" class="btn btn-info">
            {{ isVisible ? '隐藏' : '显示' }} 内容
          </button>
          <div v-if="isVisible" class="conditional-content">
            <p>这是条件渲染的内容！</p>
            <p>当前计数: {{ count }}</p>
          </div>
          <div v-else class="conditional-content">
            <p>内容已隐藏</p>
          </div>
        </div>
      </section>

      <!-- 列表渲染 -->
      <section class="feature-card full-width">
        <h3>列表渲染</h3>
        <div class="demo-content">
          <div class="list-controls">
            <button @click="addRandomItem" class="btn btn-success">添加商品</button>
            <span class="item-count">共 {{ items.length }} 件商品</span>
          </div>
          
          <div class="items-list">
            <div 
              v-for="item in items" 
              :key="item.id"
              class="item-card"
            >
              <div class="item-info">
                <h4>{{ item.name }}</h4>
                <p class="item-category">{{ item.category }}</p>
                <p class="item-price">${{ item.price }}</p>
              </div>
              <button 
                @click="removeItem(item.id)" 
                class="btn btn-danger btn-sm"
              >
                删除
              </button>
            </div>
          </div>
        </div>
      </section>

      <!-- 表单绑定 -->
      <section class="feature-card">
        <h3>表单绑定</h3>
        <div class="demo-content">
          <div class="form-group">
            <label>文本输入:</label>
            <input v-model="inputValue" type="text" placeholder="输入一些文字..." />
            <p class="form-output">输入值: {{ inputValue }}</p>
          </div>
          
          <div class="form-group">
            <label>选择框:</label>
            <select v-model="selectedOption">
              <option value="option1">选项 1</option>
              <option value="option2">选项 2</option>
              <option value="option3">选项 3</option>
            </select>
            <p class="form-output">选中: {{ selectedOption }}</p>
          </div>
        </div>
      </section>

      <!-- 事件处理 -->
      <section class="feature-card">
        <h3>事件处理</h3>
        <div class="demo-content">
          <div class="event-demo">
            <button 
              @click="count++" 
              @mouseenter="console.log('鼠标进入')"
              @mouseleave="console.log('鼠标离开')"
              class="btn btn-primary"
            >
              点击计数 ({{ count }})
            </button>
            
            <input 
              @keyup.enter="inputValue = '回车键按下!'"
              @focus="console.log('输入框获得焦点')"
              @blur="console.log('输入框失去焦点')"
              placeholder="按回车键试试..."
              class="event-input"
            />
          </div>
        </div>
      </section>
    </div>
  </div>
</template>

<style scoped>
.vapor-features {
  padding: 20px;
}

.features-header {
  text-align: center;
  margin-bottom: 30px;
}

.features-header h2 {
  color: #333;
  margin-bottom: 10px;
}

.features-header p {
  color: #666;
  font-style: italic;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
}

.feature-card {
  background: white;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.feature-card.full-width {
  grid-column: 1 / -1;
}

.feature-card h3 {
  margin: 0 0 15px 0;
  color: #667eea;
  border-bottom: 2px solid #667eea;
  padding-bottom: 5px;
}

.demo-content {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.input-group, .form-group {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.input-group label, .form-group label {
  font-weight: 600;
  color: #333;
}

.input-group input, .form-group input, .form-group select {
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
}

.output, .form-output {
  background: #f8f9fa;
  padding: 10px;
  border-radius: 4px;
  border-left: 3px solid #667eea;
  margin: 0;
}

.counter-demo {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.count {
  font-size: 18px;
  font-weight: bold;
  color: #667eea;
}

.counter-controls {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.btn {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 600;
  transition: all 0.3s ease;
}

.btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

.btn-primary { background: #667eea; color: white; }
.btn-success { background: #28a745; color: white; }
.btn-warning { background: #ffc107; color: #212529; }
.btn-secondary { background: #6c757d; color: white; }
.btn-info { background: #17a2b8; color: white; }
.btn-danger { background: #dc3545; color: white; }

.btn-sm {
  padding: 4px 8px;
  font-size: 12px;
}

.conditional-content {
  background: #e7f3ff;
  padding: 15px;
  border-radius: 4px;
  border: 1px solid #b3d9ff;
}

.list-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.item-count {
  font-weight: 600;
  color: #667eea;
}

.items-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 15px;
}

.item-card {
  background: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 6px;
  padding: 15px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.item-info h4 {
  margin: 0 0 5px 0;
  color: #333;
}

.item-category {
  margin: 0;
  font-size: 12px;
  color: #666;
  background: #e9ecef;
  padding: 2px 6px;
  border-radius: 10px;
  display: inline-block;
}

.item-price {
  margin: 5px 0 0 0;
  font-weight: bold;
  color: #28a745;
}

.event-demo {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.event-input {
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
}

.event-input:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
}
</style>
