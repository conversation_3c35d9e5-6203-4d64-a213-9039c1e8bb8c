<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, getCurrentInstance, nextTick } from 'vue'

console.log(getCurrentInstance())

// 性能测试相关状态
const itemCount = ref(1000)
const items = ref<Array<{ id: number; value: number; active: boolean }>>([])
const renderTime = ref(0)
const updateTime = ref(0)
const isUpdating = ref(false)

// 生成测试数据
const generateItems = () => {
  const start = performance.now()
  
  items.value = Array.from({ length: itemCount.value }, (_, i) => ({
    id: i,
    value: Math.floor(Math.random() * 100),
    active: Math.random() > 0.5
  }))
  
  nextTick(() => {
    const end = performance.now()
    renderTime.value = Math.round((end - start) * 100) / 100
  })
}

// 批量更新数据
const updateItems = () => {
  const start = performance.now()
  
  items.value.forEach(item => {
    item.value = Math.floor(Math.random() * 100)
    item.active = Math.random() > 0.5
  })

  nextTick(() => {
    const end = performance.now()
    updateTime.value = Math.round((end - start) * 100) / 100
  })
}

// 自动更新
let autoUpdateInterval: number | null = null

const startAutoUpdate = () => {
  if (autoUpdateInterval) return
  
  isUpdating.value = true
  autoUpdateInterval = setInterval(() => {
    updateItems()
  }, 100)
}

const stopAutoUpdate = () => {
  if (autoUpdateInterval) {
    clearInterval(autoUpdateInterval)
    autoUpdateInterval = null
  }
  isUpdating.value = false
}

// 计算属性
const activeItems = computed(() => 
  items.value.filter(item => item.active)
)

const averageValue = computed(() => {
  if (items.value.length === 0) return 0
  const sum = items.value.reduce((acc, item) => acc + item.value, 0)
  return Math.round((sum / items.value.length) * 100) / 100
})

const performanceScore = computed(() => {
  const baseScore = 1000
  const renderPenalty = renderTime.value * 10
  const updatePenalty = updateTime.value * 5
  return Math.max(0, Math.round(baseScore - renderPenalty - updatePenalty))
})

// 生命周期
onMounted(() => {
  generateItems()
})

onUnmounted(() => {
  stopAutoUpdate()
})
</script>

<template>
  <div class="performance-demo">
    <div class="demo-header">
      <h3>传统模式性能演示</h3>
      <p>展示传统 Vue 组件的渲染性能</p>
    </div>

    <div class="controls">
      <div class="control-group">
        <label for="itemCount">项目数量:</label>
        <input 
          id="itemCount"
          v-model.number="itemCount" 
          type="range" 
          min="100" 
          max="10000" 
          step="100"
          @change="generateItems"
        />
        <span>{{ itemCount }}</span>
      </div>

      <div class="control-buttons">
        <button @click="generateItems" class="btn btn-primary">
          重新生成
        </button>
        <button @click="updateItems" class="btn btn-secondary">
          更新数据
        </button>
        <button 
          @click="isUpdating ? stopAutoUpdate() : startAutoUpdate()" 
          class="btn"
          :class="isUpdating ? 'btn-danger' : 'btn-success'"
        >
          {{ isUpdating ? '停止自动更新' : '开始自动更新' }}
        </button>
      </div>
    </div>

    <div class="stats">
      <div class="stat-card">
        <div class="stat-value">{{ renderTime }}ms</div>
        <div class="stat-label">渲染时间</div>
      </div>
      <div class="stat-card">
        <div class="stat-value">{{ updateTime }}ms</div>
        <div class="stat-label">更新时间</div>
      </div>
      <div class="stat-card">
        <div class="stat-value">{{ activeItems.length }}</div>
        <div class="stat-label">活跃项目</div>
      </div>
      <div class="stat-card">
        <div class="stat-value">{{ averageValue }}</div>
        <div class="stat-label">平均值</div>
      </div>
      <div class="stat-card performance">
        <div class="stat-value">{{ performanceScore }}</div>
        <div class="stat-label">性能评分</div>
      </div>
    </div>

    <div class="items-container">
      <div class="items-header">
        <span>数据项 ({{ items.length }})</span>
        <div class="legend">
          <span class="legend-item active">活跃</span>
          <span class="legend-item inactive">非活跃</span>
        </div>
      </div>
      
      <div class="items-grid">
        <div 
          v-for="item in items" 
          :key="item.id"
          class="item"
          :class="{ active: item.active }"
        >
          <div class="item-id">{{ item.id }}</div>
          <div class="item-value">{{ item.value }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.performance-demo {
  padding: 20px;
  background: #f8f9fa;
  border-radius: 12px;
  border: 1px solid #dee2e6;
}

.demo-header {
  text-align: center;
  margin-bottom: 30px;
}

.demo-header h3 {
  margin: 0 0 10px 0;
  color: #333;
  font-size: 1.8em;
}

.demo-header p {
  margin: 0;
  color: #666;
  font-style: italic;
}

.controls {
  display: flex;
  flex-direction: column;
  gap: 20px;
  margin-bottom: 30px;
  padding: 20px;
  background: white;
  border-radius: 8px;
  border: 1px solid #e0e0e0;
}

.control-group {
  display: flex;
  align-items: center;
  gap: 15px;
}

.control-group label {
  font-weight: 600;
  min-width: 100px;
}

.control-group input[type="range"] {
  flex: 1;
  max-width: 300px;
}

.control-group span {
  font-weight: bold;
  color: #667eea;
  min-width: 60px;
}

.control-buttons {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

.btn {
  padding: 10px 20px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 600;
  transition: all 0.3s ease;
}

.btn-primary { 
  background: #28a745; 
  color: white; 
}

.btn-secondary { 
  background: #6c757d; 
  color: white; 
}

.btn-success { 
  background: #28a745; 
  color: white; 
}

.btn-danger { 
  background: #dc3545; 
  color: white; 
}

.btn:hover {
  opacity: 0.9;
  transform: translateY(-1px);
}

.stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 15px;
  margin-bottom: 30px;
}

.stat-card {
  background: white;
  padding: 20px;
  border-radius: 8px;
  text-align: center;
  border: 1px solid #e0e0e0;
  transition: transform 0.2s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
}

.stat-card.performance {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.stat-value {
  font-size: 2em;
  font-weight: bold;
  margin-bottom: 5px;
}

.stat-label {
  font-size: 0.9em;
  opacity: 0.8;
}

.items-container {
  background: white;
  border-radius: 8px;
  border: 1px solid #e0e0e0;
}

.items-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  background: #f8f9fa;
  border-bottom: 1px solid #e0e0e0;
  font-weight: 600;
}

.legend {
  display: flex;
  gap: 15px;
}

.legend-item {
  display: flex;
  align-items: center;
  font-size: 0.9em;
}

.legend-item::before {
  content: '';
  width: 12px;
  height: 12px;
  border-radius: 50%;
  margin-right: 5px;
}

.legend-item.active::before {
  background: #28a745;
}

.legend-item.inactive::before {
  background: #6c757d;
}

.items-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(80px, 1fr));
  gap: 1px;
  background: #e0e0e0;
  max-height: 400px;
}

.item {
  background: white;
  padding: 10px;
  text-align: center;
  transition: all 0.2s ease;
  cursor: pointer;
}

.item:hover {
  background: #f8f9fa;
}

.item.active {
  background: #d4edda;
  border-left: 3px solid #28a745;
}

.item-id {
  font-size: 0.8em;
  color: #666;
  margin-bottom: 5px;
}

.item-value {
  font-weight: bold;
  font-size: 1.1em;
  color: #333;
}

.item.active .item-value {
  color: #155724;
}

@media (max-width: 768px) {
  .controls {
    padding: 15px;
  }
  
  .control-group {
    flex-direction: column;
    align-items: stretch;
    gap: 10px;
  }
  
  .control-buttons {
    justify-content: center;
  }
  
  .stats {
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  }
  
  .items-grid {
    grid-template-columns: repeat(auto-fill, minmax(60px, 1fr));
  }
}
</style>