// ========== SonarQube 问题修复示例 ==========

// 1. 【修复认知复杂度】- 提取函数，减少嵌套
const USER_STATUS = {
  VALID: 'valid',
  EXPIRED: 'expired',
  INVALID: 'invalid'
};

const USER_LEVELS = {
  ADMIN: 'admin'
};

function processUserData(user, options) {
  if (!isValidUser(user)) {
    return { status: USER_STATUS.INVALID };
  }
  
  const adminPermission = findAdminPermission(user.permissions);
  if (!adminPermission) {
    return { status: USER_STATUS.INVALID };
  }
  
  return validateAdminAccess(user, options);
}

function isValid<PERSON>ser(user) {
  return user && user.isActive && user.permissions && user.permissions.length > 0;
}

function findAdminPermission(permissions) {
  return permissions.find(permission => permission.type === USER_LEVELS.ADMIN);
}

function validateAdminAccess(user, options) {
  if (!options?.strictMode || !user.lastLogin) {
    return { status: USER_STATUS.VALID, level: USER_LEVELS.ADMIN };
  }
  
  const isRecentLogin = new Date() - user.lastLogin < 86400000;
  return {
    status: isRecentLogin ? USER_STATUS.VALID : USER_STATUS.EXPIRED,
    level: USER_LEVELS.ADMIN
  };
}

// 2. 【修复代码重复】- 提取通用验证函数
const VALIDATION_MESSAGES = {
  EMPTY: 'empty',
  TOO_SHORT: 'too short',
  INVALID_FORMAT: 'invalid format',
  MISSING_AT: 'missing @'
};

function createValidator(fieldName, validations) {
  return function(value) {
    for (const validation of validations) {
      const result = validation(value);
      if (!result.isValid) {
        console.log(`${fieldName} validation failed: ${result.message}`);
        return false;
      }
    }
    return true;
  };
}

const commonValidations = {
  notEmpty: (value) => ({
    isValid: !!value,
    message: VALIDATION_MESSAGES.EMPTY
  }),
  minLength: (min) => (value) => ({
    isValid: value && value.length >= min,
    message: VALIDATION_MESSAGES.TOO_SHORT
  })
};

const emailValidations = [
  commonValidations.notEmpty,
  commonValidations.minLength(5),
  (value) => ({
    isValid: value && value.includes('@'),
    message: VALIDATION_MESSAGES.MISSING_AT
  })
];

const phoneValidations = [
  commonValidations.notEmpty,
  commonValidations.minLength(10),
  (value) => ({
    isValid: value && /^\d+$/.test(value),
    message: VALIDATION_MESSAGES.INVALID_FORMAT
  })
];

const validateEmail = createValidator('Email', emailValidations);
const validatePhone = createValidator('Phone', phoneValidations);

// 3. 【修复函数参数过多】- 使用对象参数
function createUser(userInfo) {
  const {
    firstName, lastName, email, phone, address, city, state,
    zipCode, country, birthDate, gender, occupation, company
  } = userInfo;
  
  return {
    firstName, lastName, email, phone, address, city, state,
    zipCode, country, birthDate, gender, occupation, company
  };
}

// 4. 【修复魔法数字】- 使用常量
const DISCOUNT_RATES = {
  PREMIUM: 0.15,
  GOLD: 0.10,
  SILVER: 0.05,
  DEFAULT: 0
};

function calculateDiscount(price, userType) {
  const rate = DISCOUNT_RATES[userType.toUpperCase()] || DISCOUNT_RATES.DEFAULT;
  return price * rate;
}

// 5. 【修复空catch块】- 适当的错误处理
function riskyOperation() {
  try {
    return JSON.parse('invalid json');
  } catch (error) {
    console.error('JSON parsing failed:', error.message);
    return null;
  }
}

// 6. 【修复未使用变量】- 移除或使用变量
function processData(data) {
  return data.map(item => item.value);
}

// 7. 【修复相同条件分支】- 合并条件
const SUCCESS_CODES = [200, 201, 202];

function getStatus(code) {
  return SUCCESS_CODES.includes(code) ? 'success' : 'error';
}

// 8. 【修复过长函数】- 拆分为多个小函数
const MULTIPLIERS = {
  A: 2,
  B: 3,
  C: 4,
  D: 5
};

function processDataByType(data) {
  return data.map(item => ({
    ...item,
    value: item.value * (MULTIPLIERS[item.type] || 1)
  }));
}

function filterAndSort(data) {
  return data
    .filter(item => item.value > 0)
    .sort((a, b) => a.value - b.value);
}

function formatResults(data) {
  return data.map(item => ({
    value: item.value,
    formatted: `$${item.value.toFixed(2)}`
  }));
}

function processDataEfficiently(data) {
  const processed = processDataByType(data);
  const filtered = filterAndSort(processed);
  return formatResults(filtered);
}

// 9. 【修复Switch缺少default】
function handleAction(action) {
  switch (action) {
    case 'create':
      return 'Creating...';
    case 'update':
      return 'Updating...';
    case 'delete':
      return 'Deleting...';
    default:
      return 'Unknown action';
  }
}

// 10. 【修复使用==】- 使用严格相等
function compareValues(a, b) {
  return a === b;
}

// 11. 【修复变量命名】- 使用驼峰命名和清晰的变量名
function processData(inputData) {
  const activeItems = inputData.filter(item => item.active);
  return activeItems;
}

// 12. 【修复过度嵌套三元运算符】- 使用if-else或函数
function getStatusMessage(user) {
  if (!user) return 'No User';
  if (!user.isActive) return 'Inactive User';
  if (!user.isPremium) return 'Regular Active User';
  
  return user.hasAccess ? 'Premium Active User' : 'Premium No Access';
}

// 13. 【修复重复字符串】- 使用常量
const ERROR_MESSAGES = {
  AUTH_FAILED: 'User authentication failed'
};

function logMessages() {
  console.log(ERROR_MESSAGES.AUTH_FAILED);
  console.log(ERROR_MESSAGES.AUTH_FAILED);
  console.log(ERROR_MESSAGES.AUTH_FAILED);
}

// 14. 【修复复杂布尔表达式】- 拆分为多个函数
function hasBasicAccess(user, resource) {
  return user && user.isActive && user.permissions && 
         user.permissions.includes('read') && resource;
}

function hasPublicAccess(resource) {
  return resource.isPublic;
}

function hasOwnerAccess(user, resource, time) {
  return resource.owner === user.id && 
         time > user.lastLogin && 
         user.subscription === 'premium';
}

function checkAccess(user, resource, time) {
  if (!hasBasicAccess(user, resource)) {
    return false;
  }
  
  return hasPublicAccess(resource) || hasOwnerAccess(user, resource, time);
}

// 15. 【修复返回类型不一致】- 统一返回类型
const RESULT_TYPES = {
  SUCCESS: 'success',
  ZERO: 'zero',
  FAILURE: 'failure'
};

function getValue(condition) {
  if (condition) {
    return RESULT_TYPES.SUCCESS;
  } else if (condition === null) {
    return RESULT_TYPES.ZERO;
  } else {
    return RESULT_TYPES.FAILURE;
  }
}
